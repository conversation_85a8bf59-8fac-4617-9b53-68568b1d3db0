import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enAuth from './locales/en/auth.json' with { type: 'json' };
// import enNavigation from "./locales/en/navigation.json"  with { type: 'json' };
import skAuth from './locales/sk/auth.json' with { type: 'json' };
// import skNavigation from "./locales/sk/navigation.json";

export const resources = {
  en: {
		auth: enAuth,
		// navigation: enNavigation,
  },
  sk: {
		auth: skAuth,
		// navigation: skNavigation,
  },
} as const;

i18n.use(initReactI18next).init({
  resources,
  lng: 'sk',
  fallbackLng: 'en',
  ns: ['auth'],
  defaultNS: 'common',
  interpolation: { escapeValue: false },
});

export default i18n;
