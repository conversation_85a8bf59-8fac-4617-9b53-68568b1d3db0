{"name": "data-access", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"@dohoder/source": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "nx": {"sourceRoot": "libs/data-access/src", "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "libs/data-access/dist", "main": "libs/data-access/src/index.ts", "tsConfig": "libs/data-access/tsconfig.lib.json", "skipTypeCheck": true, "stripLeadingPaths": true}}}}, "dependencies": {"@swc/helpers": "~0.5.11"}}