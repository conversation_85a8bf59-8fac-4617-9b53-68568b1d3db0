-- **************************************
-- RLS POLITIKY PRE SUPERADMIN & EDITOR
-- **************************************

-- Pomocn<PERSON> funkcie
CREATE OR REPLACE FUNCTION public.is_superadmin()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM user_role_assignment ura
        JOIN user_roles ur ON ur.id = ura.role_id
        WHERE ura.profile_id = auth.uid()
          AND ur.role_name = 'superadmin'
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_editor()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM user_role_assignment ura
        JOIN user_roles ur ON ur.id = ura.role_id
        WHERE ura.profile_id = auth.uid()
          AND ur.role_name = 'editor'
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Tabuľky, kde editor bude read-only
DO $$
DECLARE
    tbl text;
BEGIN
    FOR tbl IN SELECT unnest(array[
        'user_roles',
        'user_role_assignment',
        'app_packages',
        'package_features',
        'package_feature_assignment'
    ])
    LOOP
        EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY', tbl);
        EXECUTE format('CREATE POLICY "%I superadmin full access" ON %I FOR ALL USING (is_superadmin()) WITH CHECK (is_superadmin())', tbl, tbl);
        EXECUTE format('CREATE POLICY "%I editor read-only" ON %I FOR SELECT USING (is_editor())', tbl, tbl);
    END LOOP;
END;
$$;

-- Tabuľky aplikácie, kde editor môže INSERT + UPDATE + SELECT, ale nie DELETE
DO $$
DECLARE
    tbl text;
BEGIN
    FOR tbl IN SELECT unnest(array[
        'profiles',
        'organizations',
        'organization_access',
        'groups',
        'group_members',
        'documents',
        'events',
        'event_participants',
        'polls',
        'poll_options',
        'poll_votes',
        'billing_entities',
        'profile_billing_assignment',
        'invoices'
    ])
    LOOP
        EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY', tbl);
        EXECUTE format('CREATE POLICY "%I superadmin full access" ON %I FOR ALL USING (is_superadmin()) WITH CHECK (is_superadmin())', tbl, tbl);
        EXECUTE format('CREATE POLICY "%I editor select" ON %I FOR SELECT USING (is_editor())', tbl, tbl);
        EXECUTE format('CREATE POLICY "%I editor insert" ON %I FOR INSERT WITH CHECK (is_editor())', tbl, tbl);
        EXECUTE format('CREATE POLICY "%I editor update" ON %I FOR UPDATE USING (is_editor()) WITH CHECK (is_editor())', tbl, tbl);
    END LOOP;
END;
$$;

-- Audit logy: iba SELECT pre všetkých (vrátane superadmin)
DO $$
DECLARE
    tbl text;
BEGIN
    FOR tbl IN SELECT unnest(array[
        'audit_profiles',
        'audit_organizations',
        'audit_groups',
        'audit_billing_entities',
        'audit_invoices'
    ])
    LOOP
        EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY', tbl);
        EXECUTE format('CREATE POLICY "%I read-only" ON %I FOR SELECT USING (true)', tbl, tbl);
    END LOOP;
END;
$$;
