import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getClient } from '../supabase/client';
import {
  Todo,
  CreateTodoRequest,
  UpdateTodoRequest,
} from '@/domain/entities/todo';

// Query keys
export const todoKeys = {
  all: ['todos'] as const,
  lists: () => [...todoKeys.all, 'list'] as const,
  list: (filters: string) => [...todoKeys.lists(), { filters }] as const,
  details: () => [...todoKeys.all, 'detail'] as const,
  detail: (id: string) => [...todoKeys.details(), id] as const,
};

// Queries
export function useTodos() {
  return useQuery({
    queryKey: todoKeys.lists(),
    queryFn: async (): Promise<Todo[]> => {
      const supabase = getClient();
      const { data, error } = await supabase
        .from('todos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      // Map snake_case to camelCase
      return (data || []).map((todo: any) => ({
        id: todo.id,
        title: todo.title,
        description: todo.description,
        completed: todo.completed,
        userId: todo.user_id,
        categoryId: todo.category_id,
        createdAt: new Date(todo.created_at),
        updatedAt: new Date(todo.updated_at),
      }));
    },
  });
}

export function useTodo(id: string) {
  return useQuery({
    queryKey: todoKeys.detail(id),
    queryFn: async (): Promise<Todo> => {
      const supabase = getClient();
      const { data, error } = await supabase
        .from('todos')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Map snake_case to camelCase
      return {
        id: data.id,
        title: data.title,
        description: data.description,
        completed: data.completed,
        userId: data.user_id,
        categoryId: data.category_id,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    },
    enabled: !!id,
  });
}

// Mutations
export function useCreateTodo() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newTodo: CreateTodoRequest): Promise<Todo> => {
      const supabase = getClient();

      // Map camelCase to snake_case for database
      const dbTodo: any = {
        title: newTodo.title,
        user_id: newTodo.userId,
      };

      if (newTodo.description !== undefined) {
        dbTodo.description = newTodo.description;
      }

      if (newTodo.categoryId !== undefined) {
        dbTodo.category_id = newTodo.categoryId;
      }

      const { data, error } = await supabase
        .from('todos')
        .insert([dbTodo])
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Map snake_case back to camelCase
      return {
        id: data.id,
        title: data.title,
        description: data.description,
        completed: data.completed,
        userId: data.user_id,
        categoryId: data.category_id,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    },
    onSuccess: () => {
      // Invalidate and refetch todos list
      queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
    },
  });
}

export function useUpdateTodo() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: UpdateTodoRequest;
    }): Promise<Todo> => {
      const supabase = getClient();

      // Map camelCase to snake_case for database
      const dbUpdates: any = {};
      if (updates.title !== undefined) dbUpdates.title = updates.title;
      if (updates.description !== undefined)
        dbUpdates.description = updates.description;
      if (updates.completed !== undefined)
        dbUpdates.completed = updates.completed;
      if (updates.categoryId !== undefined)
        dbUpdates.category_id = updates.categoryId;

      const { data, error } = await supabase
        .from('todos')
        .update(dbUpdates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Map snake_case back to camelCase
      return {
        id: data.id,
        title: data.title,
        description: data.description,
        completed: data.completed,
        userId: data.user_id,
        categoryId: data.category_id,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    },
    onSuccess: (data) => {
      // Update the specific todo in cache
      queryClient.setQueryData(todoKeys.detail(data.id), data);
      // Invalidate todos list to ensure consistency
      queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
    },
  });
}

export function useDeleteTodo() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const supabase = getClient();
      const { error } = await supabase.from('todos').delete().eq('id', id);

      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: todoKeys.detail(deletedId) });
      // Invalidate todos list
      queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
    },
  });
}
