{"compilerOptions": {"baseUrl": ".", "composite": true, "declaration": true, "declarationMap": false, "emitDeclarationOnly": false, "importHelpers": true, "isolatedModules": true, "lib": ["es2022", "dom", "dom.iterable"], "module": "esnext", "moduleResolution": "bundler", "noEmitOnError": false, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "paths": {"@/data-access/*": ["libs/data-access/src/*"], "@/domain/*": ["libs/domain/src/*"], "@/ui/*": ["libs/ui/src/*"], "@/utils/*": ["libs/util/src/*"], "@/i18n/*": ["libs/i18n/src/*"]}}}