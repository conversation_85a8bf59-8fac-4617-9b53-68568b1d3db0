export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.5"
  }
  public: {
    Tables: {
      app_packages: {
        Row: {
          id: number
          is_active: boolean | null
          package_name: string
          price_eur: number
        }
        Insert: {
          id: number
          is_active?: boolean | null
          package_name: string
          price_eur: number
        }
        Update: {
          id?: number
          is_active?: boolean | null
          package_name?: string
          price_eur?: number
        }
        Relationships: []
      }
      audit_billing_entities: {
        Row: {
          action: string
          billing_entity_id: string | null
          changed_by_user_id: string | null
          created_at: string | null
          id: string
          new_data: Json | null
          old_data: Json | null
          row_id: string
        }
        Insert: {
          action: string
          billing_entity_id?: string | null
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id: string
        }
        Update: {
          action?: string
          billing_entity_id?: string | null
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_billing_entities_changed_by_user_id_fkey"
            columns: ["changed_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_groups: {
        Row: {
          action: string
          changed_by_user_id: string | null
          created_at: string | null
          group_id: string | null
          id: string
          new_data: Json | null
          old_data: Json | null
          row_id: string
        }
        Insert: {
          action: string
          changed_by_user_id?: string | null
          created_at?: string | null
          group_id?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id: string
        }
        Update: {
          action?: string
          changed_by_user_id?: string | null
          created_at?: string | null
          group_id?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_groups_changed_by_user_id_fkey"
            columns: ["changed_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_invoices: {
        Row: {
          action: string
          changed_by_user_id: string | null
          created_at: string | null
          id: string
          invoice_id: string | null
          new_data: Json | null
          old_data: Json | null
          row_id: string
        }
        Insert: {
          action: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          invoice_id?: string | null
          new_data?: Json | null
          old_data?: Json | null
          row_id: string
        }
        Update: {
          action?: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          invoice_id?: string | null
          new_data?: Json | null
          old_data?: Json | null
          row_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_invoices_changed_by_user_id_fkey"
            columns: ["changed_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_log: {
        Row: {
          action: string
          changed_by_user_id: string | null
          created_at: string | null
          id: string
          new_data: Json | null
          old_data: Json | null
          row_id: string
          table_name: string
        }
        Insert: {
          action: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id: string
          table_name: string
        }
        Update: {
          action?: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id?: string
          table_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_log_changed_by_user_id_fkey"
            columns: ["changed_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_organizations: {
        Row: {
          action: string
          changed_by_user_id: string | null
          created_at: string | null
          id: string
          new_data: Json | null
          old_data: Json | null
          organization_id: string | null
          row_id: string
        }
        Insert: {
          action: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          organization_id?: string | null
          row_id: string
        }
        Update: {
          action?: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          organization_id?: string | null
          row_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_organizations_changed_by_user_id_fkey"
            columns: ["changed_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_profiles: {
        Row: {
          action: string
          changed_by_user_id: string | null
          created_at: string | null
          id: string
          new_data: Json | null
          old_data: Json | null
          row_id: string
        }
        Insert: {
          action: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id: string
        }
        Update: {
          action?: string
          changed_by_user_id?: string | null
          created_at?: string | null
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          row_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_profiles_changed_by_user_id_fkey"
            columns: ["changed_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      billing_entities: {
        Row: {
          bank_account_iban: string
          city: string
          country: string
          dic: string | null
          entity_name: string
          ico: string | null
          id: string
          is_company: boolean
          is_vat_payer: boolean
          managed_by_profile_id: string
          organization_id: string | null
          street: string
          updated_at: string | null
          zip_code: string
        }
        Insert: {
          bank_account_iban: string
          city: string
          country: string
          dic?: string | null
          entity_name: string
          ico?: string | null
          id?: string
          is_company: boolean
          is_vat_payer: boolean
          managed_by_profile_id: string
          organization_id?: string | null
          street: string
          updated_at?: string | null
          zip_code: string
        }
        Update: {
          bank_account_iban?: string
          city?: string
          country?: string
          dic?: string | null
          entity_name?: string
          ico?: string | null
          id?: string
          is_company?: boolean
          is_vat_payer?: boolean
          managed_by_profile_id?: string
          organization_id?: string | null
          street?: string
          updated_at?: string | null
          zip_code?: string
        }
        Relationships: [
          {
            foreignKeyName: "billing_entities_managed_by_profile_id_fkey"
            columns: ["managed_by_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_entities_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      color_themes: {
        Row: {
          created_at: string | null
          id: string
          primary_hex: string
          secondary_hex: string
          theme_name: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          primary_hex: string
          secondary_hex: string
          theme_name: string
        }
        Update: {
          created_at?: string | null
          id?: string
          primary_hex?: string
          secondary_hex?: string
          theme_name?: string
        }
        Relationships: []
      }
      documents: {
        Row: {
          created_at: string | null
          document_name: string
          file_type: string
          group_id: string | null
          id: string
          organization_id: string | null
          storage_path: string
          uploaded_by_id: string
        }
        Insert: {
          created_at?: string | null
          document_name: string
          file_type: string
          group_id?: string | null
          id?: string
          organization_id?: string | null
          storage_path: string
          uploaded_by_id: string
        }
        Update: {
          created_at?: string | null
          document_name?: string
          file_type?: string
          group_id?: string | null
          id?: string
          organization_id?: string | null
          storage_path?: string
          uploaded_by_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_uploaded_by_id_fkey"
            columns: ["uploaded_by_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      event_participants: {
        Row: {
          attendance_status: Database["public"]["Enums"]["attendance_status"]
          event_id: string
          profile_id: string
          voted_at: string | null
        }
        Insert: {
          attendance_status: Database["public"]["Enums"]["attendance_status"]
          event_id: string
          profile_id: string
          voted_at?: string | null
        }
        Update: {
          attendance_status?: Database["public"]["Enums"]["attendance_status"]
          event_id?: string
          profile_id?: string
          voted_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_participants_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_participants_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          created_at: string | null
          created_by_id: string
          description: string | null
          end_time: string
          event_name: string
          group_id: string
          id: string
          is_archived: boolean | null
          location: string | null
          start_time: string
        }
        Insert: {
          created_at?: string | null
          created_by_id: string
          description?: string | null
          end_time: string
          event_name: string
          group_id: string
          id?: string
          is_archived?: boolean | null
          location?: string | null
          start_time: string
        }
        Update: {
          created_at?: string | null
          created_by_id?: string
          description?: string | null
          end_time?: string
          event_name?: string
          group_id?: string
          id?: string
          is_archived?: boolean | null
          location?: string | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_created_by_id_fkey"
            columns: ["created_by_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      group_members: {
        Row: {
          entity_role: Database["public"]["Enums"]["entity_role"]
          group_id: string
          is_active: boolean | null
          profile_id: string
        }
        Insert: {
          entity_role: Database["public"]["Enums"]["entity_role"]
          group_id: string
          is_active?: boolean | null
          profile_id: string
        }
        Update: {
          entity_role?: Database["public"]["Enums"]["entity_role"]
          group_id?: string
          is_active?: boolean | null
          profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_members_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      groups: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          description: string | null
          group_name: string
          id: string
          is_archived: boolean | null
          organization_id: string | null
          owner_profile_id: string
          package_id: number | null
          package_valid_until: string | null
          theme_id: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          description?: string | null
          group_name: string
          id?: string
          is_archived?: boolean | null
          organization_id?: string | null
          owner_profile_id: string
          package_id?: number | null
          package_valid_until?: string | null
          theme_id?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          description?: string | null
          group_name?: string
          id?: string
          is_archived?: boolean | null
          organization_id?: string | null
          owner_profile_id?: string
          package_id?: number | null
          package_valid_until?: string | null
          theme_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "groups_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "groups_owner_profile_id_fkey"
            columns: ["owner_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "groups_package_id_fkey"
            columns: ["package_id"]
            isOneToOne: false
            referencedRelation: "app_packages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "groups_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "color_themes"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          amount_due: number
          billing_entity_id: string | null
          created_at: string | null
          currency: string
          due_date: string
          id: string
          invoice_number: string
          organization_id: string | null
          paid_at: string | null
          payment_gateway_ref: string | null
          status: Database["public"]["Enums"]["invoice_status"]
        }
        Insert: {
          amount_due: number
          billing_entity_id?: string | null
          created_at?: string | null
          currency?: string
          due_date: string
          id?: string
          invoice_number: string
          organization_id?: string | null
          paid_at?: string | null
          payment_gateway_ref?: string | null
          status: Database["public"]["Enums"]["invoice_status"]
        }
        Update: {
          amount_due?: number
          billing_entity_id?: string | null
          created_at?: string | null
          currency?: string
          due_date?: string
          id?: string
          invoice_number?: string
          organization_id?: string | null
          paid_at?: string | null
          payment_gateway_ref?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
        }
        Relationships: [
          {
            foreignKeyName: "invoices_billing_entity_id_fkey"
            columns: ["billing_entity_id"]
            isOneToOne: false
            referencedRelation: "billing_entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          body: string
          created_at: string | null
          id: string
          read_status: boolean | null
          recipient_id: string
          target_url: string | null
          title: string
        }
        Insert: {
          body: string
          created_at?: string | null
          id?: string
          read_status?: boolean | null
          recipient_id: string
          target_url?: string | null
          title: string
        }
        Update: {
          body?: string
          created_at?: string | null
          id?: string
          read_status?: boolean | null
          recipient_id?: string
          target_url?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_access: {
        Row: {
          entity_role: Database["public"]["Enums"]["entity_role"]
          organization_id: string
          profile_id: string
        }
        Insert: {
          entity_role: Database["public"]["Enums"]["entity_role"]
          organization_id: string
          profile_id: string
        }
        Update: {
          entity_role?: Database["public"]["Enums"]["entity_role"]
          organization_id?: string
          profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_access_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_access_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          description: string | null
          id: string
          org_name: string
          owner_profile_id: string
          package_id: number | null
          package_valid_until: string | null
          theme_id: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          org_name: string
          owner_profile_id: string
          package_id?: number | null
          package_valid_until?: string | null
          theme_id?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          org_name?: string
          owner_profile_id?: string
          package_id?: number | null
          package_valid_until?: string | null
          theme_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_owner_profile_id_fkey"
            columns: ["owner_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organizations_package_id_fkey"
            columns: ["package_id"]
            isOneToOne: false
            referencedRelation: "app_packages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organizations_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "color_themes"
            referencedColumns: ["id"]
          },
        ]
      }
      package_feature_assignment: {
        Row: {
          feature_id: number
          feature_limit_value: number | null
          package_id: number
        }
        Insert: {
          feature_id: number
          feature_limit_value?: number | null
          package_id: number
        }
        Update: {
          feature_id?: number
          feature_limit_value?: number | null
          package_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "package_feature_assignment_feature_id_fkey"
            columns: ["feature_id"]
            isOneToOne: false
            referencedRelation: "package_features"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "package_feature_assignment_package_id_fkey"
            columns: ["package_id"]
            isOneToOne: false
            referencedRelation: "app_packages"
            referencedColumns: ["id"]
          },
        ]
      }
      package_features: {
        Row: {
          description: string
          feature_key: string
          id: number
        }
        Insert: {
          description: string
          feature_key: string
          id: number
        }
        Update: {
          description?: string
          feature_key?: string
          id?: number
        }
        Relationships: []
      }
      poll_options: {
        Row: {
          id: string
          option_text: string
          poll_id: string
          sort_order: number
        }
        Insert: {
          id?: string
          option_text: string
          poll_id: string
          sort_order: number
        }
        Update: {
          id?: string
          option_text?: string
          poll_id?: string
          sort_order?: number
        }
        Relationships: [
          {
            foreignKeyName: "poll_options_poll_id_fkey"
            columns: ["poll_id"]
            isOneToOne: false
            referencedRelation: "polls"
            referencedColumns: ["id"]
          },
        ]
      }
      poll_votes: {
        Row: {
          option_id: string
          poll_id: string
          voted_at: string | null
          voter_id: string
        }
        Insert: {
          option_id: string
          poll_id: string
          voted_at?: string | null
          voter_id: string
        }
        Update: {
          option_id?: string
          poll_id?: string
          voted_at?: string | null
          voter_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "poll_votes_option_id_fkey"
            columns: ["option_id"]
            isOneToOne: false
            referencedRelation: "poll_options"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "poll_votes_poll_id_fkey"
            columns: ["poll_id"]
            isOneToOne: false
            referencedRelation: "polls"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "poll_votes_voter_id_fkey"
            columns: ["voter_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      polls: {
        Row: {
          created_at: string | null
          created_by_id: string
          description: string | null
          expires_at: string
          group_id: string
          id: string
          is_active: boolean | null
          is_multichoice: boolean | null
          question: string
        }
        Insert: {
          created_at?: string | null
          created_by_id: string
          description?: string | null
          expires_at: string
          group_id: string
          id?: string
          is_active?: boolean | null
          is_multichoice?: boolean | null
          question: string
        }
        Update: {
          created_at?: string | null
          created_by_id?: string
          description?: string | null
          expires_at?: string
          group_id?: string
          id?: string
          is_active?: boolean | null
          is_multichoice?: boolean | null
          question?: string
        }
        Relationships: [
          {
            foreignKeyName: "polls_created_by_id_fkey"
            columns: ["created_by_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "polls_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      profile_billing_assignment: {
        Row: {
          billing_entity_id: string
          profile_id: string
        }
        Insert: {
          billing_entity_id: string
          profile_id: string
        }
        Update: {
          billing_entity_id?: string
          profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "profile_billing_assignment_billing_entity_id_fkey"
            columns: ["billing_entity_id"]
            isOneToOne: false
            referencedRelation: "billing_entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_billing_assignment_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          first_name: string
          id: string
          is_active: boolean | null
          last_name: string | null
          phone_number: string | null
          preferred_language: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          first_name: string
          id: string
          is_active?: boolean | null
          last_name?: string | null
          phone_number?: string | null
          preferred_language?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          first_name?: string
          id?: string
          is_active?: boolean | null
          last_name?: string | null
          phone_number?: string | null
          preferred_language?: string | null
        }
        Relationships: []
      }
      user_role_assignment: {
        Row: {
          profile_id: string
          role_id: number
        }
        Insert: {
          profile_id: string
          role_id: number
        }
        Update: {
          profile_id?: string
          role_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_role_assignment_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_role_assignment_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "user_roles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          id: number
          role_name: string
        }
        Insert: {
          id: number
          role_name: string
        }
        Update: {
          id?: number
          role_name?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      is_editor: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_superadmin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      attendance_status: "attending" | "absent" | "maybe"
      entity_role: "owner" | "admin" | "member"
      invoice_status: "draft" | "sent" | "paid" | "overdue" | "cancelled"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      attendance_status: ["attending", "absent", "maybe"],
      entity_role: ["owner", "admin", "member"],
      invoice_status: ["draft", "sent", "paid", "overdue", "cancelled"],
    },
  },
} as const
