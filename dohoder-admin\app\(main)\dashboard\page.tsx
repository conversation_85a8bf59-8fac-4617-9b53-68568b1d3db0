'use client';

import { useEffect, useState } from 'react';
// import SignOutButton from '@/components/sign-out-button';
import { getClient } from '@/data-access/supabase/client';

export default function ClientDashboardPage() {
  const [userName, setUserName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const client = getClient();

  useEffect(() => {
    // Načítanie mena prihláseného používateľa
    async function loadUserData() {
      const {
        data: { user },
      } = await client.auth.getUser();

      if (user) {
        const profileId = user.id;

        const { data: profileData, error: profileError } = await client
          .from('profiles')
          .select('first_name')
          .eq('id', profileId)
          .limit(1)
          .single();

        if (profileData) {
          setUserName(profileData.first_name);
        } else {
          setUserName(user.email || '<PERSON><PERSON>');
        }
      }
      setIsLoading(false);
    }

    loadUserData();
  }, [client]);

  if (isLoading) {
    return (
      <div className="p-20 text-center text-xl">
        Načítavam používateľské dáta...
      </div>
    );
  }

  // Použijeme zistené meno alebo fallback
  const greetingName = userName || 'Prihlásený Používateľ';

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-4xl font-extrabold text-gray-900 mb-6">
        Vitaj späť, {greetingName}!
      </h1>

      <p className="text-lg text-gray-600 mb-8">
        Toto je klientsky dashboard, ktorý sa dynamicky mení na základe
        prihláseného používateľa. Máte plný prístup ku klientskemu Supabase API.
      </p>

      {/* Tlačidlo na odhlásenie, ktoré je tiež Client Component */}
      <>
        <button
          onClick={async () => {
            await client.auth.signOut();
            window.location.href = '/login';
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Odhlásiť sa
        </button>
      </>
    </div>
  );
}
