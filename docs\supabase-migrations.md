# Supabase Migrácie - Návod

## 🎯 Prehľad

Tento projekt používa **Supabase migrácie** pre správu databázovej schémy. Migrácie umožňujú:

- ✅ Verziovanie databázovej schémy
- ✅ Synchronizáciu medzi vývojárskymi prostrediami
- ✅ Bezpečné nasadenie zmien do produkcie
- ✅ Rollback možnosti
- ✅ Kolaboráciu v tíme

## 📁 Štruktúra

```
supabase/
├── config.toml                              # Supabase konfigurácia
├── migrations/                              # SQL migračné súbory
│   └── 20251003013000_create_base_schema.sql # Z<PERSON><PERSON><PERSON> schéma (22 tabuliek)
└── seed.sql                                 # Seed dáta (superadmin + roly)
```

## 🔧 Základné príkazy

### Lokálny vývoj (Docker)

```bash
# Spustenie lokálnej Supabase
npm run db:start

# Reset databázy + migrácie + seed
npm run db:reset

# Zastavenie lokálnej Supabase
npm run db:stop
```

### Migrácie

```bash
# Vytvorenie novej migrácie
npm run db:migration:new nazov_migracie

# Príklad
npm run db:migration:new add_user_avatar_column
```

### Práca s produkčnou databázou

```bash
# Push migrácií na produkciu (vyžaduje SUPABASE_ACCESS_TOKEN)
npm run db:push

# Push migrácií + seed dáta
npm run db:seed

# Stiahnutie schémy z produkcie
npm run db:pull
```

### Pripojenie k projektu (jednorazovo)

```bash
# Pripojenie k existujúcemu Supabase projektu
npx supabase link --project-ref YOUR_PROJECT_REF
```

## 📝 Príklady migrácií

### Pridanie nového stĺpca

```sql
-- Pridanie avatar_url stĺpca do users tabuľky
ALTER TABLE public.users
ADD COLUMN avatar_url TEXT;

-- Vytvorenie indexu
CREATE INDEX IF NOT EXISTS users_avatar_url_idx ON public.users(avatar_url);
```

### Vytvorenie novej tabuľky

```sql
-- Vytvorenie categories tabuľky
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    color TEXT DEFAULT '#000000',
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- RLS politiky
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own categories" ON public.categories
    USING (auth.uid() = user_id);

-- Trigger pre updated_at
CREATE TRIGGER handle_categories_updated_at
    BEFORE UPDATE ON public.categories
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();
```

### Pridanie vzťahu medzi tabuľkami

```sql
-- Pridanie category_id do todos tabuľky
ALTER TABLE public.todos
ADD COLUMN category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL;

-- Index pre lepší výkon
CREATE INDEX IF NOT EXISTS todos_category_id_idx ON public.todos(category_id);
```

## 🔒 Row Level Security (RLS)

Všetky tabuľky majú zapnuté RLS pre bezpečnosť:

```sql
-- Zapnutie RLS
ALTER TABLE public.table_name ENABLE ROW LEVEL SECURITY;

-- Základné politiky
CREATE POLICY "Users can view their own data" ON public.table_name
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own data" ON public.table_name
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own data" ON public.table_name
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own data" ON public.table_name
    FOR DELETE USING (auth.uid() = user_id);
```

## 🌱 Seed dáta

Seed súbor `supabase/seed.sql` obsahuje ukážkové dáta pre development:

```bash
# Aplikovanie seed dát
npx supabase db push --include-seed
```

## ⚠️ Dôležité poznámky

### Bezpečnosť

- **Nikdy** necommitujte citlivé dáta do migrácií
- Používajte environment premenné pre citlivé konfigurácie
- Vždy testujte migrácie na staging prostredí pred produkciou

### Best Practices

- Používajte popisné názvy pre migrácie
- Jedna migrácia = jedna logická zmena
- Vždy pridajte `IF NOT EXISTS` pre CREATE príkazy
- Používajte transakcie pre komplexné zmeny
- Dokumentujte zložité migrácie komentármi

### Rollback

```sql
-- Príklad rollback migrácie
-- Ak pridávate stĺpec, rollback ho odstráni
ALTER TABLE public.users DROP COLUMN IF EXISTS avatar_url;
```

## 🚀 Workflow

### Lokálny vývoj

1. **Spustite lokálnu DB**: `npm run db:start`
2. **Vytvorte migráciu**: `npm run db:migration:new feature_name`
3. **Napíšte SQL**: Editujte vytvorený súbor v `supabase/migrations/`
4. **Testujte lokálne**: `npm run db:reset`
5. **Commitnite zmeny**: Git commit migračných súborov

### Nasadenie na produkciu

1. **Nastavte token**: Pridajte `SUPABASE_ACCESS_TOKEN` do `.env`
2. **Push migrácie**: `npm run db:push`
3. **Overenie**: Skontrolujte v Supabase Dashboard

## 📚 Užitočné odkazy

- [Supabase CLI dokumentácia](https://supabase.com/docs/guides/cli)
- [Migrácie best practices](https://supabase.com/docs/guides/database/migrations)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
