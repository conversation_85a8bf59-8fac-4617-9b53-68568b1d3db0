# 🗄️ Databázov<PERSON> štruktúra

## 📋 Prehľad

Databáza obsahuje **22 hlavných entít** organizovaných do logických skupín s komplexným systémom oprávnений a hierarchie.

## 🏗️ Architektúra

### ENUM typy

```sql
-- Roly v organizáciách/skupinách
entity_role: 'owner', 'admin', 'member'

-- <PERSON>av ú<PERSON> na udalostiach  
attendance_status: 'attending', 'absent', 'maybe'

-- <PERSON>av faktúr
invoice_status: 'draft', 'sent', 'paid', 'overdue', 'cancelled'
```

## 📊 Hlavné entity

### 👥 Používatelia a autentifikácia

#### `profiles`
<PERSON><PERSON><PERSON>n<PERSON> profily pou<PERSON>ov (nezávislé od auth.users)
- `id` (uuid, PK) - Prim<PERSON><PERSON><PERSON> k<PERSON>
- `first_name`, `last_name`, `email` - <PERSON><PERSON><PERSON><PERSON><PERSON>
- `phone_number`, `avatar_url` - Kontaktné info
- `is_active`, `preferred_language` - Nastavenia

#### `user_roles` + `user_role_assignment`
Globálne systémové roly (superadmin, editor, billing_manager, analyst)
- Umožňuje priradenie viacerých rolí jednému používateľovi

### 🎨 Systémové entity

#### `color_themes`
Farebné témy pre organizácie/skupiny
- `theme_name`, `primary_hex`, `secondary_hex`

#### `notifications`
<PERSON>ystémové notifikácie pre používateľov
- `recipient_id` → profiles(id)
- `title`, `body`, `target_url`
- `read_status`

#### `audit_log`
Audit trail pre sledovanie zmien
- `table_name`, `row_id`, `action`
- `old_data`, `new_data` (jsonb)
- `changed_by_user_id` → profiles(id)

### 💰 Monetizácia

#### `app_packages`
Cenové balíčky aplikácie
- `package_name`, `price_eur`
- `is_active`

#### `package_features` + `package_feature_assignment`
Funkcie a ich limity v balíčkach
- `feature_key`, `description`
- `feature_limit_value` - číselný limit pre funkciu

### 🏢 Hierarchia organizácií

#### `organizations`
Najvyššia úroveň hierarchie
- `org_name`, `description`, `avatar_url`
- `theme_id` → color_themes(id)
- `package_id` → app_packages(id) *(nullable)*
- `package_valid_until` *(nullable)*
- `owner_profile_id` → profiles(id) **ON DELETE RESTRICT**

#### `organization_access`
Prístup používateľov k organizáciám
- `organization_id` + `profile_id` (composite PK)
- `entity_role` (owner/admin/member)

#### `groups`
Skupiny v rámci organizácií
- `organization_id` → organizations(id) **ON DELETE RESTRICT**
- `group_name`, `description`, `avatar_url`
- `theme_id`, `package_id`, `package_valid_until`
- `owner_profile_id` → profiles(id) **ON DELETE RESTRICT**
- `is_archived`

#### `group_members`
Členstvo v skupinách
- `group_id` + `profile_id` (composite PK)
- `entity_role`, `is_active`

### 📄 Dokumenty

#### `documents`
Súbory priložené k organizáciám/skupinám
- `group_id` → groups(id) OR `organization_id` → organizations(id)
- `uploaded_by_id` → profiles(id)
- `document_name`, `storage_path`, `file_type`
- **Constraint**: Musí byť priradený k skupine ALEBO organizácii

### 📅 Udalosti

#### `events`
Udalosti v skupinách
- `group_id` → groups(id) **NOT NULL**
- `event_name`, `description`
- `start_time`, `end_time`, `location`
- `created_by_id` → profiles(id)
- `is_archived`

#### `event_participants`
Účasť na udalostiach
- `event_id` + `profile_id` (composite PK)
- `attendance_status` (attending/absent/maybe)
- `voted_at`

### 🗳️ Hlasovanie

#### `polls`
Ankety v skupinách
- `group_id` → groups(id) **NOT NULL**
- `question`, `description`
- `expires_at`, `is_active`
- `is_multichoice` - umožňuje viacero odpovedí
- `created_by_id` → profiles(id)

#### `poll_options`
Možnosti v anketách
- `poll_id` → polls(id)
- `option_text`, `sort_order`

#### `poll_votes`
Hlasy v anketách
- `poll_id` + `voter_id` + `option_id` (composite PK)
- `voted_at`

### 💳 Fakturácia

#### `billing_entities`
Fakturačné subjekty (firmy/osoby)
- `entity_name`, `is_company`, `is_vat_payer`
- `ico`, `dic`, `bank_account_iban`
- `street`, `city`, `zip_code`, `country`
- `managed_by_profile_id` → profiles(id) **ON DELETE RESTRICT**
- `organization_id` → organizations(id)

#### `profile_billing_assignment`
Priradenie profilu k fakturačnému subjektu
- `profile_id` (PK) → profiles(id)
- `billing_entity_id` → billing_entities(id) **ON DELETE RESTRICT**

#### `invoices`
Faktúry
- `invoice_number` (unique)
- `billing_entity_id` → billing_entities(id) *(nullable)*
- `organization_id` → organizations(id) *(nullable)*
- `amount_due`, `currency`, `status`
- `due_date`, `paid_at`
- `payment_gateway_ref`

## 🔒 Bezpečnosť (RLS)

### Row Level Security

Všetky kľúčové tabuľky majú zapnuté RLS s politikami:

```sql
-- Príklad politiky
CREATE POLICY "Users can view their own data" 
ON table_name FOR SELECT 
USING (auth.uid() = user_id);
```

### Superadmin prístup

```sql
-- Funkcia pre overenie superadmin role
CREATE FUNCTION is_superadmin() RETURNS boolean
-- Kontroluje či má používateľ role_id = 1
```

Superadmin má bypass prístup k všetkým dátam cez špeciálne RLS politiky.

## 🔗 Kľúčové vzťahy

### Hierarchia
```
Organizations (1) → (N) Groups → (N) Events/Polls/Documents
```

### Prístupové práva
```
Profiles → Organization_Access → Organizations
Profiles → Group_Members → Groups
```

### Fakturácia
```
Profiles → Profile_Billing_Assignment → Billing_Entities → Invoices
```

### Monetizácia
```
App_Packages → Package_Feature_Assignment → Package_Features
Organizations/Groups → App_Packages (subscription)
```

## ⚠️ Dôležité poznámky

### DELETE Constraints
- **RESTRICT**: Blokuje zmazanie ak existujú závislé záznamy
  - `owner_profile_id` - vlastníci blokujú zmazanie
  - `organization_id` v groups - org sa nezmaže kým má skupiny
  
- **CASCADE**: Automaticky zmaže závislé záznamy
  - Členstvá, účasť na udalostiach, hlasy

### Nullable polia
Niektoré polia sú dočasne nullable (označené komentármi v migrácii):
- `package_id` a `package_valid_until` v organizations/groups
- `billing_entity_id` a `organization_id` v invoices

### Audit Trail
Všetky zmeny sú sledované cez `audit_log` tabuľku s JSON snapshot-mi starých/nových hodnôt.
