{"name": "domain", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "nx": {"sourceRoot": "libs/domain/src", "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "libs/domain/dist", "main": "libs/domain/src/index.ts", "tsConfig": "libs/domain/tsconfig.lib.json", "skipTypeCheck": true, "stripLeadingPaths": true}}}}, "dependencies": {"@swc/helpers": "~0.5.11"}}