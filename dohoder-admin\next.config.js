//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  // SPA mode - no SSR/SSG, pure client-side
  output: 'export',
  trailingSlash: true,
  distDir: 'dist',

  // Disable image optimization for static export
  images: {
    unoptimized: true,
  },

  // Experimental features (if needed)
  experimental: {
    // Add experimental features here if needed
  },

  // Use this to set Nx-specific options
  // See: https://nx.dev/recipes/next/next-config-setup
  nx: {
    // Disable deprecated SVGR support from Nx
    svgr: false,
  },

  compiler: {
    // Enable Emotion support
    emotion: true,
  },

  // Manual SVGR configuration (if needed)
  webpack(config) {
    // Add custom SVG handling if needed
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });
    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
