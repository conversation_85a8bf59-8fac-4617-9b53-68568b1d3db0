# React Query + Supabase Setup Guide

## 🎯 Prehľad

Projekt teraz používa **TanStack Query (React Query)** pre efektívne data fetching s **Supabase** databázou. Tento setup poskytuje:

- ✅ Automatické cachovanie dát
- ✅ Background refetching
- ✅ Optimistic updates
- ✅ Offline support (mobile)
- ✅ Loading a error states
- ✅ Devtools pre debugging

## 📁 Štruktúra

### Supabase klienti

```
libs/data-access/src/supabase/
├── client.ts          # Mobile Supabase klient (s persistSession pre offline)
├── next-client.ts     # Next.js Supabase klient (optimalizovaný pre web)
├── next-server.ts     # Server-side Supabase klient (pre Next.js SSR)
└── client-factory.ts  # Factory pre výber správneho klienta podľa platformy
```

### React Query hooks

```
libs/data-access/src/queries/
├── todos.ts           # Todo CRUD operácie s React Query
├── users.ts           # User CRUD operácie s React Query
└── categories.ts      # Category CRUD operácie s React Query
```

**Poznámka:** <PERSON><PERSON><PERSON><PERSON> hooks automaticky používajú správny Supabase klient podľa platformy:

- **Next.js**: Používa `next-client.ts` (optimalizovaný pre web)
- **Mobile**: Používa `client.ts` (s offline support)

### Providers

```
dohoder-admin/app/providers/
└── query-provider.tsx # React Query provider pre Next.js

dohoder-mobile/src/providers/
└── QueryProvider.tsx  # React Query provider pre React Native
```

## 🔧 Konfigurácia

### Environment Variables

Potrebné premenné prostredia:

**Next.js Admin (.env.local):**

```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_KEY=your-anon-key
```

**React Native Mobile (.env):**

```bash
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_KEY=your-anon-key
```

### Query Client konfigurácia

**Next.js Admin:**

- Stale time: 1 minúta
- Retry: 3x (okrem 4xx chýb)
- React Query Devtools zapnuté
- **Bez persistor** (nepotrebný pre web aplikácie)

**React Native Mobile:**

- Stale time: 5 minút
- Cache time: 10 minút
- **Offline persistence s AsyncStorage** (pre offline support)
- Retry: 3x (okrem 4xx chýb)

## 📝 Použitie

### Základné query hooks

```typescript
import {
  useTodos,
  useCreateTodo,
  useUpdateTodo,
  useDeleteTodo,
} from '@/data-access/queries/todos';

function TodoComponent() {
  // Načítanie todos
  const { data: todos, isLoading, error } = useTodos();

  // Mutations
  const createTodoMutation = useCreateTodo();
  const updateTodoMutation = useUpdateTodo();
  const deleteTodoMutation = useDeleteTodo();

  // Vytvorenie nového todo
  const handleCreate = async () => {
    await createTodoMutation.mutateAsync({
      title: 'Nové todo',
      userId: 'user-id',
    });
  };

  // Aktualizácia todo
  const handleUpdate = async (id: string) => {
    await updateTodoMutation.mutateAsync({
      id,
      updates: { completed: true },
    });
  };

  // Zmazanie todo
  const handleDelete = async (id: string) => {
    await deleteTodoMutation.mutateAsync(id);
  };
}
```

### Query keys pattern

```typescript
export const todoKeys = {
  all: ['todos'] as const,
  lists: () => [...todoKeys.all, 'list'] as const,
  list: (filters: string) => [...todoKeys.lists(), { filters }] as const,
  details: () => [...todoKeys.all, 'detail'] as const,
  detail: (id: string) => [...todoKeys.details(), id] as const,
};
```

### Optimistic updates

```typescript
const updateTodoMutation = useMutation({
  mutationFn: updateTodoApi,
  onMutate: async (newTodo) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({ queryKey: todoKeys.lists() });

    // Snapshot previous value
    const previousTodos = queryClient.getQueryData(todoKeys.lists());

    // Optimistically update
    queryClient.setQueryData(todoKeys.lists(), (old) =>
      old?.map((todo) =>
        todo.id === newTodo.id ? { ...todo, ...newTodo.updates } : todo
      )
    );

    return { previousTodos };
  },
  onError: (err, newTodo, context) => {
    // Rollback on error
    queryClient.setQueryData(todoKeys.lists(), context?.previousTodos);
  },
  onSettled: () => {
    // Always refetch after error or success
    queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
  },
});
```

## 🚀 Výhody

### Pre vývojárov:

- **Jednoduchšie data fetching** - žiadne manuálne loading states
- **Automatické cachovanie** - dáta sa načítajú len raz
- **Background updates** - dáta sa aktualizujú na pozadí
- **Devtools** - vizuálne debugging query cache

### Pre používateľov:

- **Rýchlejšia aplikácia** - cachovanie znižuje loading časy
- **Offline podpora** - aplikácia funguje aj bez internetu (mobile)
- **Optimistic updates** - okamžitá odozva na akcie
- **Lepšie UX** - inteligentné loading a error states

## 🔍 Debugging

### React Query Devtools

**Next.js Admin:**
Devtools sú automaticky dostupné v development móde. Kliknite na floating button v dolnom rohu.

**React Native Mobile:**
Pre mobile debugging použite Flipper plugin alebo React Native Debugger.

### Query inspection

```typescript
// Manuálne invalidovanie cache
queryClient.invalidateQueries({ queryKey: todoKeys.all });

// Prefetch dát
queryClient.prefetchQuery({
  queryKey: todoKeys.lists(),
  queryFn: fetchTodos,
});

// Nastavenie dát do cache
queryClient.setQueryData(todoKeys.detail('123'), newTodoData);
```

## 📚 Ďalšie možnosti

### Infinite queries

Pre pagination a infinite scroll:

```typescript
const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
  useInfiniteQuery({
    queryKey: ['todos', 'infinite'],
    queryFn: ({ pageParam = 0 }) => fetchTodos(pageParam),
    getNextPageParam: (lastPage, pages) => lastPage.nextCursor,
  });
```

### Mutations s optimistic updates

Pre okamžitú odozvu:

```typescript
const mutation = useMutation({
  mutationFn: updateTodo,
  onMutate: async (newTodo) => {
    // Optimistic update logic
  },
  onError: (err, newTodo, context) => {
    // Rollback logic
  },
});
```

### Dependent queries

Pre queries závislé od iných dát:

```typescript
const { data: user } = useUser();
const { data: todos } = useTodos(user?.id, {
  enabled: !!user?.id, // Spustí sa len ak máme user ID
});
```

## 🎉 Záver

React Query setup poskytuje robustnú a škálovateľnú architektúru pre data management v oboch aplikáciách. Automaticky rieši väčšinu problémov spojených s data fetchingom a poskytuje skvelý developer experience.
