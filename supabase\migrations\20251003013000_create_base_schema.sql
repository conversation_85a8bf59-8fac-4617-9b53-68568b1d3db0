-- **************************************
-- I. PRÍPRAVNÉ KROKY (EXTENSIONS A ENUMY)
-- **************************************

-- Povoliť pgcrypto pre UUID
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Vytvorenie ENUM typov
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'entity_role') THEN
        CREATE TYPE entity_role AS ENUM ('owner', 'admin', 'member');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'attendance_status') THEN
        CREATE TYPE attendance_status AS ENUM ('attending', 'absent', 'maybe');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status') THEN
        CREATE TYPE invoice_status AS ENUM ('draft', 'sent', 'paid', 'overdue', 'cancelled');
    END IF;
END $$;

-- **************************************
-- II. CREATE TABLE (22 ENTÍT)
-- **************************************

-- ZÁKLADNÉ A SYSTÉMOVÉ
CREATE TABLE profiles (
    id uuid PRIMARY KEY,
    first_name text NOT NULL,
    last_name text,
    email text UNIQUE NOT NULL,
    phone_number text,
    is_active boolean DEFAULT TRUE,
    preferred_language text DEFAULT 'sk',
    avatar_url text,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE user_roles (
    id smallint PRIMARY KEY,
    role_name text UNIQUE NOT NULL
);

CREATE TABLE user_role_assignment (
    profile_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    role_id smallint REFERENCES user_roles(id) ON DELETE CASCADE,
    PRIMARY KEY (profile_id, role_id)
);

CREATE TABLE color_themes (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    theme_name text NOT NULL,
    primary_hex text NOT NULL,
    secondary_hex text NOT NULL,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE notifications (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    title text NOT NULL,
    body text NOT NULL,
    target_url text,
    read_status boolean DEFAULT FALSE,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE audit_log (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name text NOT NULL,
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    created_at timestamptz DEFAULT now()
);

-- MONETIZÁCIA A LIMITY
CREATE TABLE app_packages (
    id smallint PRIMARY KEY,
    package_name text UNIQUE NOT NULL,
    price_eur numeric NOT NULL,
    is_active boolean DEFAULT TRUE
);

CREATE TABLE package_features (
    id smallint PRIMARY KEY,
    feature_key text UNIQUE NOT NULL,
    description text NOT NULL
);

CREATE TABLE package_feature_assignment (
    package_id smallint REFERENCES app_packages(id) ON DELETE CASCADE,
    feature_id smallint REFERENCES package_features(id) ON DELETE CASCADE,
    feature_limit_value smallint,
    PRIMARY KEY (package_id, feature_id)
);

-- HIERARCHIA A PRÍSTUP (S RESTRICT)
CREATE TABLE organizations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    org_name text NOT NULL,
    description text,
    avatar_url text,
    theme_id uuid REFERENCES color_themes(id) ON DELETE SET NULL,
    package_id smallint REFERENCES app_packages(id) NOT NULL,
    package_valid_until timestamptz NOT NULL,
    owner_profile_id uuid REFERENCES profiles(id) ON DELETE RESTRICT NOT NULL,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE organization_access (
    organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
    profile_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    entity_role entity_role NOT NULL,
    PRIMARY KEY (organization_id, profile_id)
);

CREATE TABLE groups (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid REFERENCES organizations(id) ON DELETE RESTRICT,
    group_name text NOT NULL,
    description text,
    avatar_url text,
    theme_id uuid REFERENCES color_themes(id) ON DELETE SET NULL,
    package_id smallint REFERENCES app_packages(id) NOT NULL,
    package_valid_until timestamptz NOT NULL,
    owner_profile_id uuid REFERENCES profiles(id) ON DELETE RESTRICT NOT NULL,
    is_archived boolean DEFAULT FALSE,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE group_members (
    group_id uuid REFERENCES groups(id) ON DELETE CASCADE,
    profile_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    entity_role entity_role NOT NULL,
    is_active boolean DEFAULT TRUE,
    PRIMARY KEY (group_id, profile_id)
);

-- BIZNIS FUNKCIONALITA
CREATE TABLE documents (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id uuid REFERENCES groups(id) ON DELETE CASCADE,
    organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
    uploaded_by_id uuid REFERENCES profiles(id) ON DELETE SET NULL NOT NULL,
    document_name text NOT NULL,
    storage_path text NOT NULL,
    file_type text NOT NULL,
    created_at timestamptz DEFAULT now(),
    CONSTRAINT check_entity_link CHECK (group_id IS NOT NULL OR organization_id IS NOT NULL)
);

CREATE TABLE events (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id uuid REFERENCES groups(id) ON DELETE CASCADE NOT NULL,
    event_name text NOT NULL,
    description text,
    start_time timestamptz NOT NULL,
    end_time timestamptz NOT NULL,
    location text,
    is_archived boolean DEFAULT FALSE,
    created_by_id uuid REFERENCES profiles(id) ON DELETE SET NULL NOT NULL,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE event_participants (
    event_id uuid REFERENCES events(id) ON DELETE CASCADE,
    profile_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    attendance_status attendance_status NOT NULL,
    voted_at timestamptz DEFAULT now(),
    PRIMARY KEY (event_id, profile_id)
);

CREATE TABLE polls (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id uuid REFERENCES groups(id) ON DELETE CASCADE NOT NULL,
    question text NOT NULL,
    description text,
    expires_at timestamptz NOT NULL,
    is_active boolean DEFAULT TRUE,
    is_multichoice boolean DEFAULT FALSE,
    created_by_id uuid REFERENCES profiles(id) ON DELETE SET NULL NOT NULL,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE poll_options (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    poll_id uuid REFERENCES polls(id) ON DELETE CASCADE NOT NULL,
    option_text text NOT NULL,
    sort_order smallint NOT NULL
);

CREATE TABLE poll_votes (
    poll_id uuid REFERENCES polls(id) ON DELETE CASCADE,
    option_id uuid REFERENCES poll_options(id) ON DELETE CASCADE,
    voter_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    voted_at timestamptz DEFAULT now(),
    PRIMARY KEY (poll_id, voter_id, option_id)
);

-- FAKTURÁCIA A FINANCIE
CREATE TABLE billing_entities (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_name text NOT NULL,
    is_company boolean NOT NULL,
    is_vat_payer boolean NOT NULL,
    ico text UNIQUE,
    dic text,
    bank_account_iban text NOT NULL,
    street text NOT NULL,
    city text NOT NULL,
    zip_code text NOT NULL,
    country text NOT NULL,
    managed_by_profile_id uuid REFERENCES profiles(id) ON DELETE RESTRICT NOT NULL,
    organization_id uuid REFERENCES organizations(id) ON DELETE SET NULL,
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE profile_billing_assignment (
    profile_id uuid PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    billing_entity_id uuid REFERENCES billing_entities(id) ON DELETE RESTRICT NOT NULL
);

CREATE TABLE invoices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_number text UNIQUE NOT NULL,
    billing_entity_id uuid REFERENCES billing_entities(id) ON DELETE RESTRICT NOT NULL,
    organization_id uuid REFERENCES organizations(id) ON DELETE RESTRICT NOT NULL,
    amount_due numeric NOT NULL,
    currency text NOT NULL DEFAULT 'EUR',
    status invoice_status NOT NULL,
    due_date date NOT NULL,
    paid_at timestamptz,
    payment_gateway_ref text,
    created_at timestamptz DEFAULT now()
);

-- **************************************
-- III. NULLABLE ÚPRAVY
-- **************************************
ALTER TABLE organizations 
    ALTER COLUMN package_id DROP NOT NULL,
    ALTER COLUMN package_valid_until DROP NOT NULL;

ALTER TABLE groups 
    ALTER COLUMN package_id DROP NOT NULL,
    ALTER COLUMN package_valid_until DROP NOT NULL;

ALTER TABLE invoices
    ALTER COLUMN billing_entity_id DROP NOT NULL,
    ALTER COLUMN organization_id DROP NOT NULL;

-- **************************************
-- IV. AUTOMATIZÁCIA A TRIGGERS
-- **************************************
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, first_name)
  VALUES (new.id, new.email, 'Nový Užívateľ');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE PROCEDURE public.handle_new_user();

-- **************************************
-- V. RLS FUNKCIE A POLITIKY
-- **************************************
CREATE OR REPLACE FUNCTION is_superadmin()
RETURNS boolean AS $$
    SELECT EXISTS (
        SELECT 1
        FROM user_role_assignment
        WHERE profile_id = auth.uid() AND role_id = 1
    );
$$ LANGUAGE sql SECURITY DEFINER;

-- Zapnutie RLS na kľúčových tabuľkách
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE profile_billing_assignment ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Superadmin politika
CREATE POLICY "Superadmin bypass access on organizations" 
ON organizations 
FOR ALL 
USING (is_superadmin()) 
WITH CHECK (is_superadmin());

CREATE POLICY "Superadmin full access to audit_log" 
ON audit_log 
FOR ALL 
USING (is_superadmin()) 
WITH CHECK (is_superadmin());
