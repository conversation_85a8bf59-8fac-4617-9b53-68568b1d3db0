-- Povoliť pgcrypto pre gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- **************************************
-- I. AUDIT LOG TABUĽKY
-- **************************************
CREATE TABLE IF NOT EXISTS audit_profiles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_organizations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    organization_id uuid,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_groups (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    group_id uuid,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_billing_entities (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    billing_entity_id uuid,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_invoices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    invoice_id uuid,
    created_at timestamptz DEFAULT now()
);

-- **************************************
-- II. AUDIT TRIGGER FUNKCIA
-- **************************************
CREATE OR REPLACE FUNCTION public.fn_audit()
RETURNS TRIGGER AS $$
DECLARE
    target_table text := TG_TABLE_NAME;
BEGIN
    IF target_table = 'profiles' THEN
        INSERT INTO audit_profiles(row_id, action, changed_by_user_id, old_data, new_data, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), NOW());
    ELSIF target_table = 'organizations' THEN
        INSERT INTO audit_organizations(row_id, action, changed_by_user_id, old_data, new_data, organization_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    ELSIF target_table = 'groups' THEN
        INSERT INTO audit_groups(row_id, action, changed_by_user_id, old_data, new_data, group_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    ELSIF target_table = 'billing_entities' THEN
        INSERT INTO audit_billing_entities(row_id, action, changed_by_user_id, old_data, new_data, billing_entity_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    ELSIF target_table = 'invoices' THEN
        INSERT INTO audit_invoices(row_id, action, changed_by_user_id, old_data, new_data, invoice_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- **************************************
-- III. TRIGGERS NA TABUĽKY
-- **************************************
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'audit_profiles_trigger') THEN
        CREATE TRIGGER audit_profiles_trigger
        AFTER INSERT OR UPDATE OR DELETE ON profiles
        FOR EACH ROW EXECUTE FUNCTION public.fn_audit();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'audit_organizations_trigger') THEN
        CREATE TRIGGER audit_organizations_trigger
        AFTER INSERT OR UPDATE OR DELETE ON organizations
        FOR EACH ROW EXECUTE FUNCTION public.fn_audit();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'audit_groups_trigger') THEN
        CREATE TRIGGER audit_groups_trigger
        AFTER INSERT OR UPDATE OR DELETE ON groups
        FOR EACH ROW EXECUTE FUNCTION public.fn_audit();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'audit_billing_entities_trigger') THEN
        CREATE TRIGGER audit_billing_entities_trigger
        AFTER INSERT OR UPDATE OR DELETE ON billing_entities
        FOR EACH ROW EXECUTE FUNCTION public.fn_audit();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'audit_invoices_trigger') THEN
        CREATE TRIGGER audit_invoices_trigger
        AFTER INSERT OR UPDATE OR DELETE ON invoices
        FOR EACH ROW EXECUTE FUNCTION public.fn_audit();
    END IF;
END;
$$;

-- **************************************
-- IV. RLS HELPER FUNKCIE
-- **************************************
CREATE OR REPLACE FUNCTION public.is_superadmin()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM user_role_assignment ura
        JOIN user_roles ur ON ur.id = ura.role_id
        WHERE ura.profile_id = auth.uid()
          AND ur.role_name = 'superadmin'
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_editor()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM user_role_assignment ura
        JOIN user_roles ur ON ur.id = ura.role_id
        WHERE ura.profile_id = auth.uid()
          AND ur.role_name = 'editor'
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- **************************************
-- V. RLS POLITIKY PRE AUDIT LOGY
-- **************************************
ALTER TABLE audit_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_billing_entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_invoices ENABLE ROW LEVEL SECURITY;

-- Dynamické vytváranie politík pre superadmin a editor
DO $$
DECLARE
    tbl text;
BEGIN
    -- Superadmin = full access
    FOR tbl IN SELECT unnest(array[
        'audit_profiles',
        'audit_organizations',
        'audit_groups',
        'audit_billing_entities',
        'audit_invoices'
    ])
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_policies
            WHERE schemaname = 'public'
              AND tablename = tbl
              AND policyname = tbl || '_superadmin_full_access'
        ) THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR ALL USING (is_superadmin()) WITH CHECK (is_superadmin())',
                tbl || '_superadmin_full_access',
                tbl
            );
        END IF;

        -- Editor = read + update + insert, no delete
        IF NOT EXISTS (
            SELECT 1 FROM pg_policies
            WHERE schemaname = 'public'
              AND tablename = tbl
              AND policyname = tbl || '_editor_select'
        ) THEN
            EXECUTE format('CREATE POLICY %I ON %I FOR SELECT USING (is_editor())',
                tbl || '_editor_select', tbl);
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM pg_policies
            WHERE schemaname = 'public'
              AND tablename = tbl
              AND policyname = tbl || '_editor_update'
        ) THEN
            EXECUTE format('CREATE POLICY %I ON %I FOR UPDATE USING (is_editor()) WITH CHECK (is_editor())',
                tbl || '_editor_update', tbl);
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM pg_policies
            WHERE schemaname = 'public'
              AND tablename = tbl
              AND policyname = tbl || '_editor_insert'
        ) THEN
            EXECUTE format('CREATE POLICY %I ON %I FOR INSERT WITH CHECK (is_editor())',
                tbl || '_editor_insert', tbl);
        END IF;
    END LOOP;
END;
$$;
