{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "composite": false, "declaration": false, "declarationMap": false, "emitDeclarationOnly": false, "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "forceConsistentCasingInFileNames": true, "types": ["node"]}, "include": ["src/**/*.ts"], "references": [], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}