'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/ui/components/button';
import { formatDate, formatRelativeTime } from '@/utils/date';
import {
  useTodos,
  useCreateTodo,
  useUpdateTodo,
  useDeleteTodo,
} from '@/data-access/queries/todos';
import { CreateTodoRequest } from '@/domain/entities/todo';

export default function Page() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration issues during static export
  if (!mounted) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">Načítavam...</div>
        </div>
      </div>
    );
  }

  return <TodoPage />;
}

function TodoPage() {
  const [isCreating, setIsCreating] = useState(false);
  const [newTodoTitle, setNewTodoTitle] = useState('');
  const [newTodoDescription, setNewTodoDescription] = useState('');

  const { data: todos, isLoading, error } = useTodos();
  const createTodoMutation = useCreateTodo();
  const updateTodoMutation = useUpdateTodo();
  const deleteTodoMutation = useDeleteTodo();

  const handleCreateTodo = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTodoTitle.trim()) return;

    const newTodo: CreateTodoRequest = {
      title: newTodoTitle.trim(),
      description: newTodoDescription.trim() || undefined,
      userId: '00000000-0000-0000-0000-000000000000', // Temporary valid UUID
    };

    try {
      await createTodoMutation.mutateAsync(newTodo);
      setNewTodoTitle('');
      setNewTodoDescription('');
      setIsCreating(false);
    } catch (error) {
      console.error('Failed to create todo:', error);
    }
  };

  const handleToggleComplete = async (todoId: string, completed: boolean) => {
    try {
      await updateTodoMutation.mutateAsync({
        id: todoId,
        updates: { completed: !completed },
      });
    } catch (error) {
      console.error('Failed to update todo:', error);
    }
  };

  const handleDeleteTodo = async (todoId: string) => {
    if (!confirm('Naozaj chcete zmazať toto todo?')) return;

    try {
      await deleteTodoMutation.mutateAsync(todoId);
    } catch (error) {
      console.error('Failed to delete todo:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">Načítavam todos...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Chyba pri načítavaní todos: {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Todo List</h1>
        <Button
          variant="primary"
          onClick={() => setIsCreating(true)}
          disabled={isCreating}
        >
          Pridať Todo
        </Button>
      </div>

      {isCreating && (
        <form
          onSubmit={handleCreateTodo}
          className="mb-6 p-4 border rounded-lg bg-gray-50"
        >
          <div className="mb-4">
            <label
              htmlFor="title"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Názov
            </label>
            <input
              type="text"
              id="title"
              value={newTodoTitle}
              onChange={(e) => setNewTodoTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Zadajte názov todo..."
              required
            />
          </div>
          <div className="mb-4">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Popis (voliteľný)
            </label>
            <textarea
              id="description"
              value={newTodoDescription}
              onChange={(e) => setNewTodoDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Zadajte popis..."
              rows={3}
            />
          </div>
          <div className="flex gap-2">
            <Button
              type="submit"
              variant="primary"
              disabled={createTodoMutation.isPending}
            >
              {createTodoMutation.isPending ? 'Vytvárám...' : 'Vytvoriť'}
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                setIsCreating(false);
                setNewTodoTitle('');
                setNewTodoDescription('');
              }}
            >
              Zrušiť
            </Button>
          </div>
        </form>
      )}

      {todos && todos.length > 0 ? (
        <ul className="space-y-3">
          {todos.map((todo) => (
            <li key={todo.id} className="p-4 border rounded-lg">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={todo.completed}
                      onChange={() =>
                        handleToggleComplete(todo.id, todo.completed)
                      }
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <h3
                      className={`font-semibold ${
                        todo.completed ? 'line-through text-gray-500' : ''
                      }`}
                    >
                      {todo.title}
                    </h3>
                  </div>
                  {todo.description && (
                    <p className="text-gray-600 mt-1 ml-7">
                      {todo.description}
                    </p>
                  )}
                  <div className="text-sm text-gray-500 mt-2 ml-7">
                    Vytvorené: {formatDate(new Date(todo.createdAt), 'long')}(
                    {formatRelativeTime(new Date(todo.createdAt))})
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="small"
                    variant="danger"
                    onClick={() => handleDeleteTodo(todo.id)}
                    disabled={deleteTodoMutation.isPending}
                  >
                    {deleteTodoMutation.isPending ? 'Mažem...' : 'Zmazať'}
                  </Button>
                </div>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <div className="text-center py-8 text-gray-500">
          Žiadne todos nenájdené
        </div>
      )}
    </div>
  );
}
