# 🚀 Development Setup - Kompletný návod

## 📋 Predpoklady

Pred začatím práce na projekte sa uistite, že máte nainštalované:

- **Node.js** (v18+)
- **NPM** 
- **Git**
- **Docker Desktop** (pre lokálny Supabase vývoj)

## 🔧 Základné nastavenie

### 1. Klonovanie a inštalácia

```bash
# Klonujte repozitár
git clone <repository-url>
cd dohoder

# Nainštalujte závislosti
npm install
```

### 2. Konfigurácia prostredia

Vytvorte `.env` súbor v koreňovom adresári projektu:

```bash
# .env (v koreni projektu)
# Supabase CLI - potrebné pre prácu s produkčnou databázou
SUPABASE_ACCESS_TOKEN=your_supabase_access_token_here

# Voliteľné - pre lokálne testovanie s produkčnou DB
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_KEY=your_anon_key_here
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=your_anon_key_here
```

> ⚠️ **Dôležité**: Nikdy necommitujte skutočné tokeny do Git repozitára!

## 🐳 Lokálny vývoj s Supabase

### Spustenie lokálnej databázy

```bash
# Spustenie lokálnej Supabase (vyžaduje Docker)
npm run db:start

# Po prvom spustení - reset a aplikovanie migrácií
npm run db:reset
```

Po spustení budú dostupné:
- **Supabase Studio**: http://localhost:54323
- **API**: http://localhost:54321
- **Database**: localhost:54322

### Základné databázové príkazy

```bash
# Lokálny vývoj
npm run db:start          # Spustenie lokálnej Supabase
npm run db:stop           # Zastavenie lokálnej Supabase
npm run db:reset          # Reset DB + aplikovanie migrácií + seed

# Migrácie
npm run db:migration:new nazov_migracie  # Vytvorenie novej migrácie

# Práca s produkčnou databázou (vyžaduje SUPABASE_ACCESS_TOKEN)
npm run db:push           # Push migrácií na produkciu
npm run db:pull           # Pull schémy z produkcie
npm run db:seed           # Push migrácií + seed dáta
```

## 🚀 Spúšťanie aplikácií

### 📱 Mobile aplikácia (React Native/Expo)

```bash
# Základný development server
npm run start:mobile

# Špecifické platformy
npm run start:mobile:web      # Web verzia
npm run start:mobile:ios      # iOS (vyžaduje macOS)
npm run start:mobile:android  # Android

# Build a export
npm run build:mobile          # Export aplikácie
npm run prebuild:mobile       # Generovanie natívnych projektov
```

### 💻 Admin aplikácia (Next.js)

```bash
# Development server
npm run start:admin

# Build
npm run build:admin

# Testy
npm run test:admin
```

## 🔄 Workflow pre vývoj

### 1. Lokálny vývoj

```bash
# 1. Spustite lokálnu databázu
npm run db:start

# 2. V novom termináli - spustite aplikácie
npm run start:mobile    # Terminal 1
npm run start:admin     # Terminal 2
```

### 2. Práca s databázou

```bash
# Vytvorenie novej migrácie
npm run db:migration:new add_new_feature

# Editujte súbor v supabase/migrations/
# Aplikujte lokálne
npm run db:reset

# Po testovaní - push na produkciu
npm run db:push
```

### 3. Synchronizácia s produkciou

```bash
# Stiahnutie najnovšej schémy z produkcie
npm run db:pull

# Push lokálnych zmien na produkciu
npm run db:push

# Push so seed dátami
npm run db:seed
```

## 🛠️ Užitočné príkazy

### Nx workspace

```bash
# Zobrazenie project graph
npx nx graph

# Spustenie konkrétneho targetu
npx nx <target> <project-name>

# Reset Nx cache
npm run nx:reset
```

### Docker

```bash
# Docker Compose build
npm run docker:build
```

### Testovanie

```bash
# Všetky testy
npx nx run-many --target=test

# Konkrétne projekty
npm run test:mobile
npm run test:admin
```

## 🔧 Riešenie problémov

### Docker problémy

```bash
# Reštart Docker Desktop
# Vyčistenie Docker cache
docker system prune -a

# Reštart Supabase
npm run db:stop
npm run db:start
```

### Port konflikty

Ak sú porty obsadené, môžete ich zmeniť v `supabase/config.toml`:
- API: port 54321
- Database: port 54322  
- Studio: port 54323

### Migračné problémy

```bash
# Reset lokálnej databázy
npm run db:reset

# Ak zlyhá - manuálny reset
npm run db:stop
docker volume prune
npm run db:start
```

## 📚 Ďalšie zdroje

- [Supabase CLI dokumentácia](https://supabase.com/docs/guides/cli)
- [Nx dokumentácia](https://nx.dev)
- [Expo dokumentácia](https://docs.expo.dev)
- [Next.js dokumentácia](https://nextjs.org/docs)
