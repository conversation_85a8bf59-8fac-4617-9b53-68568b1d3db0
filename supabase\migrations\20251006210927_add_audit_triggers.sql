-- Povoliť pgcrypto pre gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- **************************************
-- I. AUDIT LOG TABUĽKY
-- **************************************
CREATE TABLE IF NOT EXISTS audit_profiles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_organizations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    organization_id uuid,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_groups (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    group_id uuid,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_billing_entities (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    billing_entity_id uuid,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS audit_invoices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    row_id uuid NOT NULL,
    action text NOT NULL,
    changed_by_user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
    old_data jsonb,
    new_data jsonb,
    invoice_id uuid,
    created_at timestamptz DEFAULT now()
);

-- **************************************
-- II. AUDIT TRIGGER FUNKCIA
-- **************************************
CREATE OR REPLACE FUNCTION public.fn_audit()
RETURNS TRIGGER AS $$
DECLARE
    target_table text := TG_TABLE_NAME;
BEGIN
    IF target_table = 'profiles' THEN
        INSERT INTO audit_profiles(row_id, action, changed_by_user_id, old_data, new_data, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), NOW());
    ELSIF target_table = 'organizations' THEN
        INSERT INTO audit_organizations(row_id, action, changed_by_user_id, old_data, new_data, organization_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    ELSIF target_table = 'groups' THEN
        INSERT INTO audit_groups(row_id, action, changed_by_user_id, old_data, new_data, group_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    ELSIF target_table = 'billing_entities' THEN
        INSERT INTO audit_billing_entities(row_id, action, changed_by_user_id, old_data, new_data, billing_entity_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    ELSIF target_table = 'invoices' THEN
        INSERT INTO audit_invoices(row_id, action, changed_by_user_id, old_data, new_data, invoice_id, created_at)
        VALUES (COALESCE(NEW.id, OLD.id), TG_OP, auth.uid(), row_to_json(OLD), row_to_json(NEW), COALESCE(NEW.id, OLD.id), NOW());
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- **************************************
-- III. TRIGGERS NA TABUĽKY
-- **************************************
CREATE TRIGGER audit_profiles_trigger
AFTER INSERT OR UPDATE OR DELETE ON profiles
FOR EACH ROW EXECUTE FUNCTION public.fn_audit();

CREATE TRIGGER audit_organizations_trigger
AFTER INSERT OR UPDATE OR DELETE ON organizations
FOR EACH ROW EXECUTE FUNCTION public.fn_audit();

CREATE TRIGGER audit_groups_trigger
AFTER INSERT OR UPDATE OR DELETE ON groups
FOR EACH ROW EXECUTE FUNCTION public.fn_audit();

CREATE TRIGGER audit_billing_entities_trigger
AFTER INSERT OR UPDATE OR DELETE ON billing_entities
FOR EACH ROW EXECUTE FUNCTION public.fn_audit();

CREATE TRIGGER audit_invoices_trigger
AFTER INSERT OR UPDATE OR DELETE ON invoices
FOR EACH ROW EXECUTE FUNCTION public.fn_audit();

-- **************************************
-- IV. RLS HELPER FUNKCIE
-- **************************************
CREATE OR REPLACE FUNCTION public.is_superadmin()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM user_role_assignment ura
        JOIN user_roles ur ON ur.id = ura.role_id
        WHERE ura.profile_id = auth.uid()
          AND ur.role_name = 'superadmin'
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_editor()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM user_role_assignment ura
        JOIN user_roles ur ON ur.id = ura.role_id
        WHERE ura.profile_id = auth.uid()
          AND ur.role_name = 'editor'
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- **************************************
-- V. RLS POLITIKY PRE AUDIT LOGY
-- **************************************
ALTER TABLE audit_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_billing_entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_invoices ENABLE ROW LEVEL SECURITY;

-- Superadmin = full access
CREATE POLICY "Superadmin full access"
ON audit_profiles FOR ALL
USING (is_superadmin()) WITH CHECK (is_superadmin());
CREATE POLICY "Superadmin full access"
ON audit_organizations FOR ALL
USING (is_superadmin()) WITH CHECK (is_superadmin());
CREATE POLICY "Superadmin full access"
ON audit_groups FOR ALL
USING (is_superadmin()) WITH CHECK (is_superadmin());
CREATE POLICY "Superadmin full access"
ON audit_billing_entities FOR ALL
USING (is_superadmin()) WITH CHECK (is_superadmin());
CREATE POLICY "Superadmin full access"
ON audit_invoices FOR ALL
USING (is_superadmin()) WITH CHECK (is_superadmin());

-- **************************************
-- Editor = read + update + insert, no delete
-- **************************************

-- audit_profiles
CREATE POLICY "Editor select audit_profiles"
ON audit_profiles FOR SELECT
USING (is_editor());

CREATE POLICY "Editor update audit_profiles"
ON audit_profiles FOR UPDATE
USING (is_editor()) WITH CHECK (is_editor());

CREATE POLICY "Editor insert audit_profiles"
ON audit_profiles FOR INSERT
WITH CHECK (is_editor());

-- audit_organizations
CREATE POLICY "Editor select audit_organizations"
ON audit_organizations FOR SELECT
USING (is_editor());

CREATE POLICY "Editor update audit_organizations"
ON audit_organizations FOR UPDATE
USING (is_editor()) WITH CHECK (is_editor());

CREATE POLICY "Editor insert audit_organizations"
ON audit_organizations FOR INSERT
WITH CHECK (is_editor());

-- audit_groups
CREATE POLICY "Editor select audit_groups"
ON audit_groups FOR SELECT
USING (is_editor());

CREATE POLICY "Editor update audit_groups"
ON audit_groups FOR UPDATE
USING (is_editor()) WITH CHECK (is_editor());

CREATE POLICY "Editor insert audit_groups"
ON audit_groups FOR INSERT
WITH CHECK (is_editor());

-- audit_billing_entities
CREATE POLICY "Editor select audit_billing_entities"
ON audit_billing_entities FOR SELECT
USING (is_editor());

CREATE POLICY "Editor update audit_billing_entities"
ON audit_billing_entities FOR UPDATE
USING (is_editor()) WITH CHECK (is_editor());

CREATE POLICY "Editor insert audit_billing_entities"
ON audit_billing_entities FOR INSERT
WITH CHECK (is_editor());

-- audit_invoices
CREATE POLICY "Editor select audit_invoices"
ON audit_invoices FOR SELECT
USING (is_editor());

CREATE POLICY "Editor update audit_invoices"
ON audit_invoices FOR UPDATE
USING (is_editor()) WITH CHECK (is_editor());

CREATE POLICY "Editor insert audit_invoices"
ON audit_invoices FOR INSERT
WITH CHECK (is_editor());
