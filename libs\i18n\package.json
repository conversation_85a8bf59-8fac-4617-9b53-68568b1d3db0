{"name": "@dohoder/i18n", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "nx": {"sourceRoot": "libs/i18n/src", "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "libs/i18n/dist", "main": "libs/i18n/src/index.ts", "tsConfig": "libs/i18n/tsconfig.lib.json", "skipTypeCheck": true, "stripLeadingPaths": true}}}, "tags": ["shared", "frontend"]}, "dependencies": {"@swc/helpers": "~0.5.11"}}